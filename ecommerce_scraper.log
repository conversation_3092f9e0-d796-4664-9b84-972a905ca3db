2025-08-03 12:08:28,437 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:14,674 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:23,686 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,037 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:09:24,556 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,600 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:09:26,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:27,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:28,005 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:09:32,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:09:32,840 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:09:35,674 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:09:37,137 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c "HTTP/1.1 200 OK"
2025-08-03 12:09:40,277 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/navigate "HTTP/1.1 200 OK"
2025-08-03 12:09:53,749 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/act "HTTP/1.1 200 OK"
2025-08-03 12:09:59,371 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:03,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:03,479 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:07,094 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:12,220 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:17,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:17,471 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:17,547 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:22,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:22,335 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:25,914 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:30,164 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:37,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:37,991 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:39,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:40,572 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:48,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:48,509 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:49,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:49,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:50,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:51,638 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/end "HTTP/1.1 200 OK"
2025-08-03 12:11:52,363 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
