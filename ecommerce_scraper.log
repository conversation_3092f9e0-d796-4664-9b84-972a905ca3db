2025-08-03 12:08:28,437 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:14,674 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:09:23,686 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,037 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:09:24,556 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:09:24,600 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:09:26,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:27,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:09:28,005 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:09:32,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:09:32,840 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:09:35,674 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:09:37,137 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c "HTTP/1.1 200 OK"
2025-08-03 12:09:40,277 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/navigate "HTTP/1.1 200 OK"
2025-08-03 12:09:53,749 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/act "HTTP/1.1 200 OK"
2025-08-03 12:09:59,371 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:03,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:03,479 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:07,094 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:12,220 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:17,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:17,471 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:17,547 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:22,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:22,335 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:25,914 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/extract "HTTP/1.1 200 OK"
2025-08-03 12:10:30,164 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:37,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:37,991 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:39,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:40,572 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:10:48,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:10:48,509 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:10:49,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:49,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:50,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:10:51,638 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/dc65fedf-8982-4ca1-afc4-c4e0cdb4350c/end "HTTP/1.1 200 OK"
2025-08-03 12:11:52,363 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:12:37,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:37,608 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:12:38,060 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:12:38,102 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:12:38,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:12:39,601 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:12:44,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:12:44,265 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:12:47,223 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:12:48,687 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b "HTTP/1.1 200 OK"
2025-08-03 12:12:53,744 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/navigate "HTTP/1.1 200 OK"
2025-08-03 12:13:13,838 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/act "HTTP/1.1 200 OK"
2025-08-03 12:13:23,053 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:13:26,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:13:26,869 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:13:35,414 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/extract "HTTP/1.1 200 OK"
2025-08-03 12:14:06,461 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:21,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:21,552 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:21,609 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:14:55,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:14:56,004 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:14:56,122 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:09,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:09,932 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:09,973 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:26,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:26,287 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:26,302 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:43,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:43,104 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:43,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:44,771 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:15:53,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:15:53,658 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:15:54,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:55,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:56,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:57,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:15:58,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:16:00,879 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/c5b4611e-daf2-4383-bb43-5bcec39b516b/end "HTTP/1.1 200 OK"
2025-08-03 12:24:00,902 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:05,618 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:24:15,003 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:24:15,895 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,248 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:24:16,739 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:24:16,784 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:24:18,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:24:19,543 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:23,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:23,963 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:26,829 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:24:28,287 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc "HTTP/1.1 200 OK"
2025-08-03 12:24:31,394 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/navigate "HTTP/1.1 200 OK"
2025-08-03 12:24:37,064 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:40,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:40,073 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:42,087 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information...
2025-08-03 12:24:43,885 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:24:49,238 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:24:49,265 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:24:53,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:24:53,020 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:24:55,032 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Identify and analyze elements on the page to ensur...
2025-08-03 12:24:56,495 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:10,792 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:10,834 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:14,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:14,920 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:16,933 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract product title, description, prices, and ot...
2025-08-03 12:25:18,434 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/extract "HTTP/1.1 200 OK"
2025-08-03 12:25:21,879 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:25:21,907 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:27,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:27,474 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:28,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:28,694 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:33,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:33,472 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:40,485 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Check if the page is accessible and properly loade...
2025-08-03 12:25:42,029 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/observe "HTTP/1.1 200 OK"
2025-08-03 12:25:46,527 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:25:46,562 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:50,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:50,638 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:52,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:25:53,303 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:25:58,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:25:58,764 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:25:59,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:00,098 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:05,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:05,277 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:05,295 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:07,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:07,923 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:08,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:08,555 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:12,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:12,219 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:26:12,329 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:26:13,668 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/f1a127fd-8d22-42d0-9252-95d2392aadcc/end "HTTP/1.1 200 OK"
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:26:13,738 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:26:33,399 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:38,523 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:26:48,450 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:26:49,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:49,955 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:26:50,486 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:26:50,545 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:26:51,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:26:52,972 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:26:58,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:26:58,356 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:01,261 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:27:02,700 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0 "HTTP/1.1 200 OK"
2025-08-03 12:27:06,401 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:27:12,413 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:17,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:17,301 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:24,314 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:27:26,143 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/extract "HTTP/1.1 200 OK"
2025-08-03 12:27:37,287 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:27:37,308 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:47,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:47,364 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:47,411 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:27:55,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:27:55,619 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:27:56,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:27:57,902 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:02,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:02,377 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:03,653 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/navigate "HTTP/1.1 200 OK"
2025-08-03 12:28:08,482 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:24,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:24,426 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:25,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:27,080 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:37,168 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:37,176 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:38,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:39,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:40,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:41,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:28:42,127 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:28:46,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:28:46,414 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:28:53,440 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing observe command (attempt 1): Inspect the page source for structured data or hid...
2025-08-03 12:28:55,298 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/observe "HTTP/1.1 200 OK"
2025-08-03 12:28:57,288 - ecommerce_scraper.tools.stagehand_tool - INFO - Elements observed
2025-08-03 12:28:57,323 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:10,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:10,730 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:10,754 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:23,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:23,703 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:24,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:24,514 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:29:34,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:29:34,067 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:29:34,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:34,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:35,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:29:36,983 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:29:38,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/a8a91ef0-a8ed-45f2-94bf-a308b049b8a0/end "HTTP/1.1 200 OK"
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:29:38,499 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:30:24,998 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:30,935 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:30:40,410 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:30:41,356 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:41,774 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:30:42,299 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:30:42,347 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:30:43,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:30:44,513 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:30:49,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:30:49,559 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:30:52,423 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:30:53,963 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1 "HTTP/1.1 200 OK"
2025-08-03 12:31:00,808 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/navigate "HTTP/1.1 200 OK"
2025-08-03 12:31:06,857 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:10,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:10,648 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:12,659 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all product information including title, d...
2025-08-03 12:31:15,437 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/extract "HTTP/1.1 200 OK"
2025-08-03 12:31:21,709 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:31:21,730 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:27,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:27,931 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:27,984 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:39,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:39,165 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:40,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:41,320 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:31:47,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:31:47,411 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:31:48,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:49,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:31:50,003 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:31:51,410 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/aa7a1b51-cc3c-4973-8fbf-f3dd507499e1/end "HTTP/1.1 200 OK"
2025-08-03 12:31:51,583 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:31:51,584 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
2025-08-03 12:33:45,320 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:33:50,874 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:33:59,607 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:00,467 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:00,854 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:01,451 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:01,492 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:01,500 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:02,546 - ecommerce_scraper.tools.stagehand_tool - INFO - No active Stagehand session to close
2025-08-03 12:34:18,632 - ecommerce_scraper.main - INFO - Set API keys for Stagehand: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:23,962 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-08-03 12:34:33,254 - __main__ - INFO - OpenAI API key loaded: sk-proj-LCFBCcSf6I2_...
2025-08-03 12:34:34,155 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:34,536 - root - INFO - Collection found or created: Collection(name=short_term)
2025-08-03 12:34:35,049 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-03 12:34:35,093 - root - INFO - Collection found or created: Collection(name=entities)
2025-08-03 12:34:35,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:34:37,309 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:41,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:41,390 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:34:44,186 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/start "HTTP/1.1 200 OK"
2025-08-03 12:34:45,730 - httpx - INFO - HTTP Request: GET https://api.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d "HTTP/1.1 200 OK"
2025-08-03 12:34:48,875 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/navigate "HTTP/1.1 200 OK"
2025-08-03 12:34:55,117 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:34:58,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:34:58,961 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:00,973 - ecommerce_scraper.tools.stagehand_tool - INFO - Executing extract command (attempt 1): Extract all available product information includin...
2025-08-03 12:35:03,579 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/extract "HTTP/1.1 200 OK"
2025-08-03 12:35:08,931 - ecommerce_scraper.tools.stagehand_tool - INFO - Data extracted
2025-08-03 12:35:08,959 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:13,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:13,430 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:13,495 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:20,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:20,368 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:21,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:22,035 - LiteLLM - INFO - 
LiteLLM completion() model= gpt-4o; provider = openai
2025-08-03 12:35:30,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 12:35:30,930 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-03 12:35:31,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:31,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:32,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-03 12:35:33,250 - ecommerce_scraper.tools.stagehand_tool - INFO - Closing Browserbase session...
2025-08-03 12:35:34,621 - httpx - INFO - HTTP Request: POST https://api.stagehand.browserbase.com/v1/sessions/65c86d31-6030-44fb-9286-7039c524640d/end "HTTP/1.1 200 OK"
2025-08-03 12:35:34,706 - ecommerce_scraper.tools.stagehand_tool - INFO - Browserbase session closed successfully
2025-08-03 12:35:34,707 - ecommerce_scraper.tools.stagehand_tool - INFO - Cache cleared
